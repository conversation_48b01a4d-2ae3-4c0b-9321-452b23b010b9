import subprocess
import time
import os
from pathlib import Path

from gi.repository import <PERSON><PERSON><PERSON>

from fabric.utils import get_relative_path
from fabric.widgets.box import Box
from fabric.widgets.button import Button
from fabric.widgets.label import Label
from fabric.widgets.svg import Svg


class RecordingIndicator(Button):
    def __init__(self, **kwargs):
        super().__init__(name="panel-button", visible=False, **kwargs)

        self.script_path = get_relative_path("../../../scripts/screen-capture.sh")
        self.recording_start_time = None
        self.last_process_check = 0
        self.process_check_interval = 1.0  # Check process every 1 second for responsiveness
        self.timer_update_interval = 1000  # Update timer every 1 second (1000ms)
        self.status_check_interval = 2000  # Check status every 2 seconds (2000ms) for faster response

        # Timeout IDs for cleanup
        self.timer_timeout_id = None
        self.status_timeout_id = None

        self.recording_icon = Svg(
            name="indicators-icon",
            size=24,
            svg_file=get_relative_path("../../../config/assets/icons/media-record.svg"),
        )
        self.time_label = Label(
            name="recording-time-label",
            markup="00:00",
            max_width_chars=5,
            ellipsize="none",
        )

        self.recording_box = Box(
            orientation="h",
            spacing=2,
            children=[self.recording_icon, self.time_label],
            size=(80, -1),
        )

        self.add(self.recording_box)

        self.connect("clicked", self.on_stop_recording)
        self.hide()

        # Start with a small delay to ensure proper initialization
        GLib.timeout_add(100, self._delayed_init)

    def is_wf_recorder_running(self):
        """Check if wf-recorder process is actually running and recording"""
        try:
            # Check if wf-recorder process exists
            result = subprocess.run(
                ["pgrep", "-x", "wf-recorder"],
                capture_output=True,
                text=True,
                timeout=1
            )
            return result.returncode == 0
        except Exception:
            return False

    def check_recording_status(self):
        """Check recording status and update visibility accordingly"""
        current_time = time.time()

        # Optimize process checking - don't check too frequently
        if current_time - self.last_process_check < self.process_check_interval:
            if self.get_visible() and self.recording_start_time:
                self.update_timer_display()
            return True

        self.last_process_check = current_time

        try:
            # Use direct process check for better accuracy
            is_recording = self.is_wf_recorder_running()

            if is_recording:
                # Start recording indicator if not visible
                if not self.get_visible():
                    self.show()
                    # Start the timer update loop when recording begins
                    if self.timer_timeout_id is None:
                        self.timer_timeout_id = GLib.timeout_add(
                            self.timer_update_interval, self.update_timer_display
                        )

                # Get the recording start time if we don't have it
                if self.recording_start_time is None:
                    self.recording_start_time = self.get_recording_start_time()

                # Update display immediately
                self.update_timer_display()
            else:
                # Stop recording indicator
                if self.get_visible():
                    self.hide()
                    self.cleanup_recording_state()

        except Exception as e:
            print(f"[DEBUG] Error checking recording status: {e}")
            # If we can't check status, hide the indicator
            if self.get_visible():
                self.hide()
                self.cleanup_recording_state()

        return True  # Continue the timeout

    def update_timer_display(self):
        """Update the timer display with current elapsed time"""
        if not self.get_visible() or self.recording_start_time is None:
            return False  # Stop timer updates if not visible

        try:
            elapsed_seconds = int(time.time() - self.recording_start_time)
            minutes = elapsed_seconds // 60
            seconds = elapsed_seconds % 60
            time_text = f"{minutes:02d}:{seconds:02d}"

            self.time_label.set_markup(time_text)
            self.set_tooltip_text(
                f"Recording in progress ({time_text}) - Click to stop"
            )

            return True  # Continue timer updates
        except Exception as e:
            print(f"[DEBUG] Error updating timer display: {e}")
            return False

    def cleanup_recording_state(self):
        """Clean up recording state and stop timers"""
        self.recording_start_time = None

        # Stop timer updates
        if self.timer_timeout_id:
            GLib.source_remove(self.timer_timeout_id)
            self.timer_timeout_id = None

    def get_recording_start_time(self):
        """Get the recording start time from the file with better error handling"""
        start_time_file = "/tmp/recording_start_time.txt"

        try:
            # Check if file exists and is readable
            if not os.path.exists(start_time_file):
                return None

            # Get file modification time as fallback
            file_mtime = os.path.getmtime(start_time_file)

            with open(start_time_file, "r") as f:
                content = f.read().strip()
                if content:
                    recorded_time = float(content)

                    # Validate that the recorded time is reasonable
                    current_time = time.time()
                    if abs(recorded_time - current_time) > 3600:  # More than 1 hour difference
                        print(f"[DEBUG] Recording start time seems invalid: {recorded_time}")
                        return file_mtime  # Use file modification time as fallback

                    return recorded_time
                else:
                    # Empty file, use modification time
                    return file_mtime

        except (ValueError, OSError) as e:
            print(f"[DEBUG] Error reading recording start time: {e}")
            # Try to use file modification time as fallback
            try:
                return os.path.getmtime(start_time_file)
            except OSError:
                return None

    def on_stop_recording(self, *args):
        """Stop the current recording with immediate feedback"""
        try:
            # Immediately hide the indicator to provide instant feedback
            self.hide()
            self.cleanup_recording_state()

            # Update the label to show stopping state
            self.time_label.set_markup("Stopping...")
            self.set_tooltip_text("Stopping recording...")

            # Send stop command asynchronously to avoid blocking UI
            subprocess.Popen(
                [self.script_path, "record", "stop"],
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )

            print("[DEBUG] Recording stop command sent")

            # Schedule a quick status check to confirm recording stopped
            GLib.timeout_add(1000, self._verify_recording_stopped)

        except Exception as e:
            print(f"[DEBUG] Error stopping recording: {e}")
            # Still hide the indicator even if stop command fails
            self.hide()
            self.cleanup_recording_state()

    def _verify_recording_stopped(self):
        """Verify that recording has actually stopped"""
        try:
            if self.is_wf_recorder_running():
                # Recording is still running, show indicator again
                print("[DEBUG] Recording still active after stop command")
                self.show()
                # Reset the timer display
                if self.recording_start_time:
                    self.update_timer_display()
                # Restart timer updates if needed
                if self.timer_timeout_id is None:
                    self.timer_timeout_id = GLib.timeout_add(
                        self.timer_update_interval, self.update_timer_display
                    )
            else:
                # Recording successfully stopped
                print("[DEBUG] Recording confirmed stopped")
                self.hide()
                self.cleanup_recording_state()
        except Exception as e:
            print(f"[DEBUG] Error verifying recording stop: {e}")

        return False  # Don't repeat this timeout

    def _delayed_init(self):
        """Initialize the recording indicator with proper error handling"""
        try:
            # Initial status check
            self.check_recording_status()

            # Set up periodic status checking (every 5 seconds)
            self.status_timeout_id = GLib.timeout_add(
                self.status_check_interval, self.check_recording_status
            )

            print("[DEBUG] Recording indicator initialized successfully")
        except Exception as e:
            print(f"[DEBUG] Error in delayed recording indicator init: {e}")
        return False  # Don't repeat this timeout

    def destroy(self):
        """Clean up timeouts when widget is destroyed"""
        if self.timer_timeout_id:
            GLib.source_remove(self.timer_timeout_id)
            self.timer_timeout_id = None

        if self.status_timeout_id:
            GLib.source_remove(self.status_timeout_id)
            self.status_timeout_id = None

        super().destroy()
