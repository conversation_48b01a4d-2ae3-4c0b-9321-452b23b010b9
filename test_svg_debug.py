#!/usr/bin/env python3

import os
import sys
sys.path.append('.')

from fabric.utils import get_relative_path

# Test from project root directory (where get_relative_path expects to be called from)
print(f"Current working directory: {os.getcwd()}")

# Test the paths that should work
paths_to_test = [
    "config/assets/icons/bluetooth.svg",
    "config/assets/icons/wifi.svg",
    "config/assets/icons/dnd-off.svg"
]

for path in paths_to_test:
    resolved_path = get_relative_path(path)
    exists = os.path.exists(resolved_path)
    print(f"Path: {path}")
    print(f"Resolved: {resolved_path}")
    print(f"Exists: {exists}")
    if exists:
        print(f"File size: {os.path.getsize(resolved_path)} bytes")
    print("---")
