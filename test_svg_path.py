#!/usr/bin/env python3

import os
import sys
sys.path.append('.')

from fabric.utils import get_relative_path

# Test the path resolution from modules/controlcenter/ context
os.chdir('modules/controlcenter')

# Test the old path (should fail)
old_path = get_relative_path("../" + "config/assets/icons/dnd.svg")
print(f"Old path: {old_path}")
print(f"Old path exists: {os.path.exists(old_path)}")

# Test the new path (should work)
new_path = get_relative_path("../../" + "config/assets/icons/dnd.svg")
print(f"New path: {new_path}")
print(f"New path exists: {os.path.exists(new_path)}")

# Test absolute path for reference
abs_path = os.path.abspath("../../config/assets/icons/dnd.svg")
print(f"Absolute path: {abs_path}")
print(f"Absolute path exists: {os.path.exists(abs_path)}")
